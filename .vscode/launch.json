{"version": "0.2.0", "configurations": [{"name": "Next.js: debug full stack", "cwd": "${workspaceFolder}/frontend", "type": "node", "request": "launch", "program": "${workspaceFolder}/frontend/node_modules/next/dist/bin/next", "runtimeArgs": ["--inspect"], "autoAttachChildProcesses": true, "skipFiles": ["<node_internals>/**"], "serverReadyAction": {"killOnServerStop": true, "pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "webRoot": "${workspaceFolder}/frontend"}, "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///./*": "${webRoot}/*", "webpack:///*": "${webRoot}/*", "webpack:///src/*": "${webRoot}/*"}}]}