{
  "css.validate": false,
  "editor.formatOnSave": true,
  "editor.tabSize": 2,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  "typescript.preferences.importModuleSpecifier": "non-relative",
  "typescript.tsdk": "node_modules/typescript/lib",
  "files.exclude": {
    "**/node_modules/.pnpm/**": true
  },
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "typescript.disableAutomaticTypeAcquisition": true,
  // Debug settings
  "debug.allowBreakpointsEverywhere": true,
  "debug.javascript.usePreview": true,
  "debug.javascript.codelens.npmScripts": "never",
  "debug.node.autoAttach": "disabled",
  "debug.javascript.autoAttachFilter": "smart",
  "debug.javascript.breakOnConditionalError": true,
  "debug.javascript.unmapMissingSources": true
}
